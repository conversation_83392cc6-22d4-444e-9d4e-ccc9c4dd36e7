import sys

sys.path.append(r'D:\\CST Studio Suite 2022\\AMD64\\python_cst_libraries')
path = r'D:\\simulations\\model_0709.cst'



def get_parameter_list():
    project_3d = proj.get_3d()
    param_combination = project_3d.get_parameter_combination(0)
    if param_combination:
        print(f"找到 {len(param_combination)} 个参数:")
        for name, value in param_combination.items():
            print(f"  {name}: {value}")
        return param_combination
    return {}

if __name__ == "__main__":
    get_parameter_list()